import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/ids_map_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class CategoryPhotoIdsNotifier extends IdsMapNotifier {}

final categoryPhotoIdsProvider =
    NotifierProvider.autoDispose<
      CategoryPhotoIdsNotifier,
      Map<String, List<int>>
    >(CategoryPhotoIdsNotifier.new);

final categoryPhotosProvider = Provider.autoDispose
    .family<List<PhotoData>, String>((ref, categorySlug) {
      final idsMap = ref.watch(categoryPhotoIdsProvider);
      final store = ref.watch(photoStoreProvider);
      final items = <PhotoData>[];

      final ids = idsMap[categorySlug];
      if (ids == null || ids.isEmpty) return items;

      for (final id in ids) {
        final item = store[id];
        if (item != null) {
          items.add(item);
        }
      }

      return items;
    });

/// High-level service for managing category photos's reactivity.
/// This is the recommended way to manage category photos.
final class CategoryPhotosReactiveService {
  const CategoryPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get category photos list for a specific category
  List<PhotoData> getAll(String categorySlug) {
    return ref.read(categoryPhotosProvider(categorySlug));
  }

  /// Get category photo IDs for a specific category
  List<int> getAllIds(String categorySlug) {
    return ref.read(categoryPhotoIdsProvider.notifier).getIds(categorySlug) ??
        [];
  }

  /// Get all category photo IDs across all categories
  Map<String, List<int>> getAllIdsMap() {
    return ref.read(categoryPhotoIdsProvider);
  }

  /// Add a new category photo (adds to both global store and category photos list)
  void addItem(String categorySlug, PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to category photos list
    ref.read(categoryPhotoIdsProvider.notifier).addItem(categorySlug, photo.id);
  }

  /// Add multiple category photos
  void addItems(String categorySlug, List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref
        .read(categoryPhotoIdsProvider.notifier)
        .addItems(categorySlug, photoIds);
  }

  /// Remove a category photo from a specific category
  void removeFromCategory(String categorySlug, int photoId) {
    // Remove from category photos list
    ref
        .read(categoryPhotoIdsProvider.notifier)
        .removeIdFromSlug(categorySlug, photoId);
  }

  /// Remove a category entirely
  void removeCategory(String categorySlug) {
    // Remove category from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItem(categorySlug);
  }

  /// Remove multiple categories
  void removeCategories(List<String> categorySlugs) {
    if (categorySlugs.isEmpty) return;

    // Remove categories from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItems(categorySlugs);
  }

  /// Update a photo in the store (automatically reflects in category photos list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all category photos for a specific category
  void replaceAll(String categorySlug, List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref
        .read(categoryPhotoIdsProvider.notifier)
        .upsertItems(categorySlug, photoIds);
  }

  /// Clear all category photos
  void clear() {
    // Clear the category photos list
    ref.read(categoryPhotoIdsProvider.notifier).clear();
  }

  /// Clear photos for a specific category
  void clearCategory(String categorySlug) {
    // Clear the category photos list for specific category
    ref.read(categoryPhotoIdsProvider.notifier).removeItem(categorySlug);
  }

  /// Get total number of categories
  int get categoryCount => ref.read(categoryPhotoIdsProvider.notifier).length;

  /// Get category photos count for a specific category
  int getCategoryPhotoCount(String categorySlug) =>
      ref.read(categoryPhotoIdsProvider.notifier).getIdsCount(categorySlug);

  /// Get total photos count across all categories
  int get totalPhotoCount =>
      ref.read(categoryPhotoIdsProvider.notifier).totalIdsCount;

  /// Check if category photos list is empty
  bool get isEmpty => ref.read(categoryPhotoIdsProvider.notifier).isEmpty;

  /// Check if category photos list is not empty
  bool get isNotEmpty => ref.read(categoryPhotoIdsProvider.notifier).isNotEmpty;

  /// Check if a specific category has photos
  bool hasCategory(String categorySlug) =>
      ref.read(categoryPhotoIdsProvider.notifier).hasItem(categorySlug);

  /// Get all category slugs
  List<String> get allCategorySlugs =>
      ref.read(categoryPhotoIdsProvider.notifier).slugs;
}

/// Provider for the CategoryPhotosReactiveService.
final categoryPhotosReactiveServiceProvider =
    Provider.autoDispose<CategoryPhotosReactiveService>((ref) {
      return CategoryPhotosReactiveService(ref);
    });
